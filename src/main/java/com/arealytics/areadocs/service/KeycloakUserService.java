package com.arealytics.areadocs.service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.resource.RealmResource;
import org.keycloak.admin.client.resource.UsersResource;
import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import com.arealytics.areadocs.domain.User;
import com.arealytics.areadocs.dto.requestDTO.UserRequestDTO;
import com.arealytics.areadocs.enumeration.PasswordActionType;
import com.arealytics.areadocs.enumeration.PasswordActionValues;
import com.arealytics.areadocs.exception.KeycloakException;
import com.arealytics.areadocs.exception.KeycloakException.KeycloakUserCreationException;
import com.arealytics.areadocs.exception.KeycloakException.UserNotFoundException;
import com.arealytics.areadocs.exception.KeycloakException.UserStatusUpdateException;
import com.arealytics.areadocs.repository.UserRepository;

import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class KeycloakUserService {

    private final Keycloak keycloak;
    private final RestTemplate restTemplate;

    @Value("${arealytics.areadocs.redirect-uri}")
    private String redirectUri;

    @Value("${keycloak.serverUrl}")
    private String serverUrl;

    @Value("${keycloak.realm}")
    private String realm;

    @Value("${keycloak.client-id}")
    private String clientId;

    @Value("${areadocs-fe-client-id}")
    private String clientFEid;

    @Value("${keycloak.client-secret}")
    private String clientSecret;

    @Autowired
    public KeycloakUserService(
            UserRepository userRepository, Keycloak keycloak, RestTemplate restTemplate) {
        this.keycloak = keycloak;
        this.restTemplate = restTemplate;
    }

    /**
     * Create a user in Keycloak.
     *
     * @param userDTO The user data transfer object
     * @return The Keycloak user ID
     */
    public String createUser(UserRequestDTO userDTO) {
        try {
            String accessToken = getAdminAccessToken();
            if (accessToken == null) {
                log.error("Failed to obtain access token for Keycloak admin API");
                throw new KeycloakException("Failed to obtain access token");
            }

            String url = String.format(PasswordActionValues.USER_URL, serverUrl, realm);

            UserRepresentation user = new UserRepresentation();
            user.setEmail(userDTO.getEmail());
            user.setUsername(userDTO.getEmail()); // Using email as username
            user.setFirstName(userDTO.getFirstName());
            user.setLastName(userDTO.getLastName());
            user.setEnabled(true);
            user.setEmailVerified(false);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(accessToken);

            HttpEntity<UserRepresentation> requestEntity = new HttpEntity<>(user, headers);

            ResponseEntity<Void> response =
                    restTemplate.postForEntity(url, requestEntity, Void.class);

            if (response.getStatusCode() == HttpStatus.CREATED) {
                log.info(
                        "User created successfully in Keycloak with email: {}", userDTO.getEmail());
                String location = response.getHeaders().getLocation().toString();
                String keycloakId = location.substring(location.lastIndexOf('/') + 1);
                return keycloakId;
            } else {
                log.error(
                        "Failed to create user in Keycloak, status: {}", response.getStatusCode());
                throw new KeycloakUserCreationException(
                        "Failed to create user in Keycloak: HTTP "
                                + response.getStatusCode().value(),
                        null);
            }
        } catch (Exception e) {
            log.error(
                    "Error creating user in Keycloak with email {}: {}",
                    userDTO.getEmail(),
                    e.getMessage(),
                    e);
            throw new KeycloakUserCreationException(
                    "Failed to create user in Keycloak: " + e.getMessage(), e);
        }
    }

    /**
     * Send an email to a user to update their password (used for both initial setup and reset).
     *
     * @param keycloakId The Keycloak user ID
     * @param actionType The type of action (e.g., "Initial Setup" or "Password Reset") for logging
     */
    public void sendUpdatePasswordEmail(String keycloakId, PasswordActionType actionType) {
        try {
            List<String> actions = List.of("UPDATE_PASSWORD");
            sendActionEmail(keycloakId, actions, clientFEid, redirectUri, null);
            log.info("{} email sent for user ID: {}", actionType.getDisplayName(), keycloakId);
        } catch (Exception e) {
            log.error(
                    "Error sending {} email for user ID {}: {}",
                    actionType.getDisplayName(),
                    keycloakId,
                    e.getMessage(),
                    e);
            throw new UserStatusUpdateException(
                    "Failed to send "
                            + actionType.getDisplayName().toLowerCase()
                            + " email: "
                            + e.getMessage(),
                    e);
        }
    }

    /**
     * Get Keycloak user ID by email.
     *
     * @param email The email of the user
     * @return The Keycloak user ID
     */
    public String getUserIdByEmail(String email) {
        try {
            String accessToken = getAdminAccessToken();
            if (accessToken == null) {
                log.error("Failed to obtain access token for Keycloak admin API");
                throw new KeycloakException("Failed to obtain access token");
            }

            String url = String.format(PasswordActionValues.EMAIL_URL, serverUrl, realm, email);

            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(accessToken);

            HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

            ResponseEntity<UserRepresentation[]> response =
                    restTemplate.exchange(
                            url, HttpMethod.GET, requestEntity, UserRepresentation[].class);

            if (response.getStatusCode() == HttpStatus.OK
                    && response.getBody() != null
                    && response.getBody().length > 0) {
                log.debug("Found user with email: {}", email);
                return response.getBody()[0].getId();
            } else {
                log.warn("No user found with email: {}", email);
                throw new UserNotFoundException("No user found with email: " + email);
            }
        } catch (Exception e) {
            log.error("Error retrieving user ID for email {}: {}", email, e.getMessage(), e);
            throw new KeycloakException("Failed to retrieve user ID: " + e.getMessage(), e);
        }
    }

    /** Send an action email to a user (e.g., email verification or password setup). */
    public void sendActionEmail(
            String keycloakId,
            List<String> actions,
            String clientId,
            String redirectUri,
            Integer lifespan) {
        try {
            if (keycloakId == null || actions == null || actions.isEmpty()) {
                log.error("Keycloak ID or actions list is null/empty");
                throw new IllegalArgumentException("Keycloak ID and actions list are required");
            }

            String accessToken = getAdminAccessToken();
            if (accessToken == null) {
                log.error("Failed to obtain access token for Keycloak admin API");
                throw new KeycloakException("Failed to obtain access token");
            }

            String url =
                    String.format(
                            PasswordActionValues.ACTION_EMAIL_URL, serverUrl, realm, keycloakId);
            if (clientId != null || redirectUri != null || lifespan != null) {
                url += "?";
                if (clientId != null) {
                    url += "client_id=" + clientId + "&";
                }
                if (redirectUri != null) {
                    url += "redirect_uri=" + redirectUri + "&";
                }
                if (lifespan != null) {
                    url += "lifespan=" + lifespan;
                }
                url =
                        url.endsWith("&") || url.endsWith("?")
                                ? url.substring(0, url.length() - 1)
                                : url;
            }

            log.debug("Sending action email with URL: {}", url);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(accessToken);

            HttpEntity<List<String>> requestEntity = new HttpEntity<>(actions, headers);

            ResponseEntity<Void> response =
                    restTemplate.exchange(url, HttpMethod.PUT, requestEntity, Void.class);

            if (response.getStatusCode() == HttpStatus.NO_CONTENT) {
                log.info("Action email sent successfully for user ID: {}", keycloakId);
            } else {
                log.error(
                        "Failed to send action email for user ID: {}, status: {}",
                        keycloakId,
                        response.getStatusCode());
                throw new UserStatusUpdateException(
                        "Failed to send action email: HTTP " + response.getStatusCode().value(),
                        null);
            }
        } catch (Exception e) {
            log.error(
                    "Error sending action email for user ID {}: {}", keycloakId, e.getMessage(), e);
            throw new UserStatusUpdateException(
                    "Failed to send action email: " + e.getMessage(), e);
        }
    }

    @Transactional
    public void deleteUser(User user) {
        try {
            // Delete user from Keycloak
            if (user.getKeycloakId() != null) {
                Response response = keycloak.realm(realm).users().delete(user.getKeycloakId());
                if (response.getStatus() != 204) {
                    String errorBody =
                            response.hasEntity() ? response.readEntity(String.class) : "no body";
                    log.error(
                            "Failed to delete user from Keycloak: {} {}",
                            response.getStatus(),
                            errorBody);
                    throw new RuntimeException(
                            "Failed to delete user from Keycloak: "
                                    + response.getStatus()
                                    + " "
                                    + errorBody);
                }
                log.info("User deleted from Keycloak with ID: {}", user.getKeycloakId());
            }
        } catch (Exception e) {
            log.error("Error during user deletion: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to delete user: " + e.getMessage(), e);
        }
    }

    public void updateUserEnabledStatus(String keycloakId, boolean enabled) {
        try {
            if (keycloakId == null) {
                log.error("Keycloak ID is null, cannot update user enabled status");
                throw new IllegalArgumentException("Keycloak ID cannot be null");
            }

            RealmResource realmResource = keycloak.realm(realm);
            UsersResource usersResource = realmResource.users();
            UserRepresentation user = usersResource.get(keycloakId).toRepresentation();

            if (user == null) {
                log.error("User not found in Keycloak with ID: {}", keycloakId);
                throw new UserNotFoundException(
                        "User not found in Keycloak with ID: " + keycloakId);
            }

            user.setEnabled(enabled);
            usersResource.get(keycloakId).update(user);
            log.info(
                    "User enabled status updated to {} in Keycloak for ID: {}",
                    enabled,
                    keycloakId);

        } catch (UserNotFoundException e) {
            throw e;
        } catch (Exception e) {
            log.error(
                    "Error updating user enabled status in Keycloak for ID {}: {}",
                    keycloakId,
                    e.getMessage(),
                    e);
            throw new UserStatusUpdateException(
                    "Failed to update user enabled status in Keycloak", e);
        }
    }

    public void updateUserDetails(String keycloakId, UserRequestDTO userRequestDTO) {
        try {
            if (keycloakId == null) {
                log.error("Keycloak ID is null, cannot update user details");
                throw new IllegalArgumentException("Keycloak ID cannot be null");
            }

            RealmResource realmResource = keycloak.realm(realm);
            UsersResource usersResource = realmResource.users();
            UserRepresentation user = usersResource.get(keycloakId).toRepresentation();

            if (user == null) {
                log.error("User not found in Keycloak with ID: {}", keycloakId);
                throw new UserNotFoundException(
                        "User not found in Keycloak with ID: " + keycloakId);
            }

            // Update relevant fields
            user.setFirstName(userRequestDTO.getFirstName());
            user.setLastName(userRequestDTO.getLastName());
            user.setEmail(userRequestDTO.getEmail());
            user.setUsername(userRequestDTO.getEmail());

            // Update user in Keycloak
            usersResource.get(keycloakId).update(user);
            log.info("User details updated in Keycloak for ID: {}", keycloakId);

        } catch (UserNotFoundException e) {
            throw e;
        } catch (Exception e) {
            log.error(
                    "Error updating user details in Keycloak for ID {}: {}",
                    keycloakId,
                    e.getMessage(),
                    e);
            throw new RuntimeException("Failed to update user details in Keycloak", e);
        }
    }

    /** Obtain an access token using client credentials for admin API calls. */
    private String getAdminAccessToken() {
        try {
            String tokenUrl = String.format(PasswordActionValues.TOKEN_URL, serverUrl, realm);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
            body.add(PasswordActionValues.GRANT_TYPE, PasswordActionValues.CLIENT_CREDENTIALS);
            body.add(PasswordActionValues.CLIENT_ID, clientId);
            body.add(PasswordActionValues.CLIENT_SECRET, clientSecret);

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(body, headers);

            ResponseEntity<Map> response = restTemplate.postForEntity(tokenUrl, request, Map.class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                String accessToken =
                        (String) response.getBody().get(PasswordActionValues.ACCESS_TOKEN);
                log.debug("Obtained Keycloak admin access token");
                return accessToken;
            } else {
                log.error("Failed to obtain access token, status: {}", response.getStatusCode());
                throw new KeycloakException(
                        "Failed to obtain access token: HTTP " + response.getStatusCode().value());
            }
        } catch (Exception e) {
            log.error("Error obtaining access token: {}", e.getMessage(), e);
            throw new KeycloakException("Failed to obtain access token: " + e.getMessage(), e);
        }
    }

    public void bulkDisableUser() {
        try {
            String url =
                    "https://auth-dev.arealytics.com.au/realms/areadocs/bulk-disable-users/bulk-disable";

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put(
                    "usernames",
                    Arrays.asList(
                            "<EMAIL>",
                            "<EMAIL>",
                            "<EMAIL>"));

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<String> response =
                    restTemplate.exchange(url, HttpMethod.POST, entity, String.class);

        } catch (Exception e) {
            throw new RuntimeException("failedddddddd", e);
        }
    }
}
